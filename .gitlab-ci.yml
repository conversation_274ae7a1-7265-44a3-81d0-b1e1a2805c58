# 🚀 上海荷阁科技 - GitLab CI/CD 配置
# 企业级 Next.js 应用的完整 CI/CD 流水线
# 支持容器化部署、安全扫描、自动化测试报告

# ==========================================
# 全局配置
# ==========================================

# 定义流水线阶段
stages:
  - prepare      # 环境准备和依赖安装
  - test         # 单元测试和集成测试
  - security     # 安全扫描和代码质量检查
  - build        # 构建应用和Docker镜像
  - deploy       # 部署到目标环境
  - report       # 生成和发布报告

# 全局变量
variables:
  # Node.js 版本
  NODE_VERSION: "18"
  # Docker 镜像仓库
  DOCKER_REGISTRY: "$CI_REGISTRY"
  DOCKER_IMAGE: "$CI_REGISTRY_IMAGE"
  # 应用配置
  APP_NAME: "hege-tech-web"
  APP_PORT: "3000"
  # 缓存配置
  CACHE_KEY: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHA"
  # Windows 环境配置
  WINDOWS_SHELL: "powershell"

# 全局缓存配置
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .next/cache/
  policy: pull-push

# 默认镜像
default:
  image: node:18-alpine
  before_script:
    - echo "🚀 开始执行 CI/CD 流水线 - $(date)"
    - echo "📦 项目 - $APP_NAME"
    - echo "🌿 分支 - $CI_COMMIT_REF_NAME"
    - echo "📝 提交 - $CI_COMMIT_SHORT_SHA"

# ==========================================
# 准备阶段 - 环境准备和依赖安装
# ==========================================

# Windows Runner 测试作业
test:windows-runner:
  stage: prepare
  tags:
    - windows
    - shell
  script:
    - echo "🪟 测试 Windows Runner 连接..."
    - echo "当前时间 - $(Get-Date)"
    - echo "PowerShell 版本 - $($PSVersionTable.PSVersion)"
    - echo "当前目录 - $(Get-Location)"
    - echo "环境变量 CI_PROJECT_NAME - $env:CI_PROJECT_NAME"
    - echo "✅ Windows Runner 测试完成"
  only:
    - ci
    - main
    - develop

prepare:dependencies:
  stage: prepare
  script:
    - echo "📦 安装项目依赖..."
    - npm ci --cache .npm --prefer-offline
    - echo "✅ 依赖安装完成"
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/
      - .npm/
    policy: push
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour
  only:
    changes:
      - package.json
      - package-lock.json
      - "**/*.js"
      - "**/*.ts"
      - "**/*.tsx"

# ==========================================
# 测试阶段 - 单元测试和集成测试
# ==========================================

test:unit:
  stage: test
  dependencies:
    - prepare:dependencies
  script:
    - echo "🧪 运行单元测试..."
    - npm run test:ci
    - echo "📊 生成测试覆盖率报告..."
    - npm run test:coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    when: always
    reports:
      junit: coverage/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

test:lint:
  stage: test
  dependencies:
    - prepare:dependencies
  script:
    - echo "🔍 运行代码检查..."
    - npm run lint
    - echo "🎨 检查代码格式..."
    - npm run format:check
  artifacts:
    when: on_failure
    paths:
      - lint-results.json
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

test:type-check:
  stage: test
  dependencies:
    - prepare:dependencies
  script:
    - echo "🔧 TypeScript 类型检查..."
    - npm run type-check
  only:
    - main
    - develop
    - merge_requests

# ==========================================
# 安全扫描阶段
# ==========================================

security:sast:
  stage: security
  dependencies:
    - prepare:dependencies
  script:
    - echo "🔒 运行静态应用安全测试 (SAST)..."
    - npm audit --audit-level=moderate
    - echo "🔍 ESLint 安全规则检查..."
    - npx eslint . --config .cicd/security/sast/.eslintrc.security.js --format json --output-file sast-results.json || true
  artifacts:
    when: always
    reports:
      sast: sast-results.json
    paths:
      - sast-results.json
    expire_in: 1 week
  allow_failure: true
  only:
    - main
    - develop
    - merge_requests

security:dependency-scan:
  stage: security
  dependencies:
    - prepare:dependencies
  script:
    - echo "🔍 依赖漏洞扫描..."
    - npm audit --json > dependency-scan.json || true
    - echo "📋 生成依赖报告..."
    - npm ls --depth=0 > dependencies-list.txt
  artifacts:
    when: always
    reports:
      dependency_scanning: dependency-scan.json
    paths:
      - dependency-scan.json
      - dependencies-list.txt
    expire_in: 1 week
  allow_failure: true
  only:
    - main
    - develop
    - merge_requests

# ==========================================
# 构建阶段
# ==========================================

build:app:
  stage: build
  dependencies:
    - prepare:dependencies
  script:
    - echo "🏗️ 构建 Next.js 应用..."
    - npm run build
    - echo "📦 构建完成，生成静态文件"
    - ls -la .next/
  artifacts:
    paths:
      - .next/
      - out/
    expire_in: 1 day
  only:
    - main
    - develop
    - merge_requests

build:docker:
  stage: build
  image: docker:24-dind
  services:
    - docker:24-dind
  dependencies:
    - build:app
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_DRIVER: overlay2
  before_script:
    - echo "🐳 Docker 环境准备..."
    - docker info
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
  script:
    - echo "🏗️ 构建 Docker 镜像..."
    - docker build -f .cicd/docker/Dockerfile -t $DOCKER_IMAGE:$CI_COMMIT_SHA -t $DOCKER_IMAGE:latest .
    - echo "📤 推送镜像到仓库..."
    - docker push $DOCKER_IMAGE:$CI_COMMIT_SHA
    - docker push $DOCKER_IMAGE:latest
  after_script:
    - docker logout $CI_REGISTRY
  only:
    - main
    - develop

# ==========================================
# 部署阶段
# ==========================================

deploy:staging:
  stage: deploy
  image: alpine:latest
  dependencies:
    - build:docker
  environment:
    name: staging
    url: https://staging.hege-tech.cn
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $STAGING_HOST >> ~/.ssh/known_hosts
  script:
    - echo "🚀 部署到测试环境..."
    - ssh $STAGING_USER@$STAGING_HOST "cd /opt/hege-tech-web && docker-compose -f .cicd/docker/docker-compose.staging.yml pull && docker-compose -f .cicd/docker/docker-compose.staging.yml up -d"
    - echo "✅ 测试环境部署完成"
  only:
    - develop

deploy:production:
  stage: deploy
  image: alpine:latest
  dependencies:
    - build:docker
  environment:
    name: production
    url: https://hege-tech.cn
  before_script:
    - apk add --no-cache openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan $PRODUCTION_HOST >> ~/.ssh/known_hosts
  script:
    - echo "🚀 部署到生产环境..."
    - ssh $PRODUCTION_USER@$PRODUCTION_HOST "cd /opt/hege-tech-web && docker-compose -f .cicd/docker/docker-compose.prod.yml pull && docker-compose -f .cicd/docker/docker-compose.prod.yml up -d"
    - echo "✅ 生产环境部署完成"
  when: manual
  only:
    - main

# ==========================================
# 报告阶段
# ==========================================

report:summary:
  stage: report
  image: alpine:latest
  dependencies:
    - test:unit
    - security:sast
    - security:dependency-scan
  script:
    - echo "📊 生成流水线执行报告..."
    - echo "项目 - $APP_NAME" > pipeline-report.txt
    - echo "分支 - $CI_COMMIT_REF_NAME" >> pipeline-report.txt
    - echo "提交 - $CI_COMMIT_SHA" >> pipeline-report.txt
    - echo "时间 - $(date)" >> pipeline-report.txt
    - echo "状态 - 成功" >> pipeline-report.txt
  artifacts:
    paths:
      - pipeline-report.txt
    expire_in: 1 month
  when: always
  only:
    - main
    - develop

# ==========================================
# Windows 环境特殊配置
# ==========================================

.windows_runner: &windows_runner
  tags:
    - windows
    - shell
  variables:
    GIT_STRATEGY: clone
    GIT_CLEAN_FLAGS: -ffdx
  before_script:
    - echo "🪟 Windows 环境准备..."
    - $env:NODE_VERSION = "18"
    - node --version
    - npm --version

# Windows 环境测试作业
test:windows:
  <<: *windows_runner
  stage: test
  script:
    - echo "🧪 Windows 环境测试..."
    - npm ci
    - npm run test
    - npm run build
  only:
    - main
    - merge_requests
  when: manual
