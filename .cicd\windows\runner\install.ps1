# GitLab Runner Windows Installation Script
# PowerShell script for installing and configuring GitLab Runner on Windows

param(
    [string]$GitLabUrl = "http://home.spinach.cool:11991/",
    [string]$RegistrationToken = "",
    [string]$RunnerName = "HEGE-Tech-Windows",
    [string]$InstallPath = "C:\GitLab-Runner",
    [string]$BuildsPath = "D:\GitLab-Runner\builds",
    [string]$CachePath = "D:\GitLab-Runner\cache",
    [switch]$Force,
    [switch]$Help
)

# Show help information
if ($Help) {
    Write-Host "GitLab Runner Windows Installation Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\install.ps1 [parameters]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "    -GitLabUrl          GitLab server URL (default: $GitLabUrl)"
    Write-Host "    -RegistrationToken  Registration token (required)"
    Write-Host "    -RunnerName         Runner name (default: $RunnerName)"
    Write-Host "    -InstallPath        Installation path (default: $InstallPath)"
    Write-Host "    -BuildsPath         Builds directory (default: $BuildsPath)"
    Write-Host "    -CachePath          Cache directory (default: $CachePath)"
    Write-Host "    -Force              Force reinstallation"
    Write-Host "    -Help               Show this help information"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "    .\install.ps1 -RegistrationToken 'your-token-here'"
    Write-Host "    .\install.ps1 -RegistrationToken 'your-token' -RunnerName 'Custom-Runner'"
    Write-Host ""
    Write-Host "Note: Administrator privileges required" -ForegroundColor Yellow
    exit 0
}

# Check administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script requires administrator privileges" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and retry" -ForegroundColor Yellow
    exit 1
}

# Check required parameters
if ([string]::IsNullOrEmpty($RegistrationToken)) {
    Write-Host "ERROR: Missing registration token parameter" -ForegroundColor Red
    Write-Host "Please use -RegistrationToken parameter to provide registration token" -ForegroundColor Yellow
    Write-Host "Use -Help to see complete help information" -ForegroundColor Yellow
    exit 1
}

# Helper functions for colored output
function Write-Info {
    param([string]$Message)
    Write-Host "INFO: $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "SUCCESS: $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "WARNING: $Message" -ForegroundColor Yellow
}

function Write-ErrorMsg {
    param([string]$Message)
    Write-Host "ERROR: $Message" -ForegroundColor Red
}

Write-Info "开始安装 GitLab Runner..."
Write-Info "GitLab URL: $GitLabUrl"
Write-Info "Runner 名称: $RunnerName"
Write-Info "安装路径: $InstallPath"

# 检查系统要求
Write-Info "检查系统要求..."

# 检查 PowerShell 版本
$psVersion = $PSVersionTable.PSVersion
if ($psVersion.Major -lt 5) {
    Write-ErrorMsg "PowerShell 版本过低，需要 5.0 或更高版本"
    exit 1
}
Write-Success "PowerShell 版本: $($psVersion.ToString())"

# 检查 .NET Framework
try {
    $dotNetVersion = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction Stop
    if ($dotNetVersion.Release -lt 461808) {
        Write-ErrorMsg ".NET Framework 版本过低，需要 4.7.2 或更高版本"
        exit 1
    }
    Write-Success ".NET Framework 版本检查通过"
} catch {
    Write-ErrorMsg "无法检查 .NET Framework 版本"
    exit 1
}

# 检查网络连接
Write-Info "检查网络连接..."
try {
    $response = Invoke-WebRequest -Uri $GitLabUrl -UseBasicParsing -TimeoutSec 10
    Write-Success "GitLab 服务器连接正常"
} catch {
    Write-Warning "无法连接到 GitLab 服务器: $($_.Exception.Message)"
    Write-Warning "继续安装，但可能需要稍后配置网络"
}

# 停止现有服务
Write-Info "检查现有 GitLab Runner 服务..."
$existingService = Get-Service -Name "gitlab-runner" -ErrorAction SilentlyContinue
if ($existingService) {
    if ($Force) {
        Write-Info "停止现有 GitLab Runner 服务..."
        Stop-Service -Name "gitlab-runner" -Force
        Write-Success "服务已停止"
    } else {
        Write-Warning "检测到现有 GitLab Runner 服务"
        $continue = Read-Host "是否继续安装？这将停止现有服务 (y/N)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            Write-Info "安装已取消"
            exit 0
        }
        Stop-Service -Name "gitlab-runner" -Force
    }
}

# 创建目录
Write-Info "创建必要目录..."
$directories = @($InstallPath, $BuildsPath, $CachePath)
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Success "创建目录: $dir"
    } else {
        Write-Info "目录已存在: $dir"
    }
}

# 下载 GitLab Runner
Write-Info "下载 GitLab Runner..."
$runnerUrl = "https://gitlab-runner-downloads.s3.amazonaws.com/latest/binaries/gitlab-runner-windows-amd64.exe"
$runnerPath = Join-Path $InstallPath "gitlab-runner.exe"

try {
    # 使用 TLS 1.2
    [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
    
    Invoke-WebRequest -Uri $runnerUrl -OutFile $runnerPath -UseBasicParsing
    Write-Success "GitLab Runner 下载完成"
} catch {
    Write-ErrorMsg "下载 GitLab Runner 失败: $($_.Exception.Message)"
    exit 1
}

# 验证下载的文件
if (-not (Test-Path $runnerPath)) {
    Write-ErrorMsg "GitLab Runner 可执行文件不存在"
    exit 1
}

$fileSize = (Get-Item $runnerPath).Length
if ($fileSize -lt 1MB) {
    Write-ErrorMsg "下载的文件大小异常，可能下载不完整"
    exit 1
}
Write-Success "文件验证通过，大小: $([math]::Round($fileSize/1MB, 2)) MB"

# 安装服务
Write-Info "安装 GitLab Runner 服务..."
try {
    Set-Location $InstallPath
    & .\gitlab-runner.exe install --user "NT AUTHORITY\SYSTEM" --password ""
    Write-Success "GitLab Runner 服务安装完成"
} catch {
    Write-ErrorMsg "安装服务失败: $($_.Exception.Message)"
    exit 1
}

# 复制配置文件
Write-Info "配置 GitLab Runner..."
$configSource = Join-Path $PSScriptRoot "config.toml"
$configDest = Join-Path $InstallPath "config.toml"

if (Test-Path $configSource) {
    Copy-Item $configSource $configDest -Force
    Write-Success "配置文件已复制"
    
    # 更新配置文件中的路径
    $configContent = Get-Content $configDest -Raw
    $configContent = $configContent -replace 'D:\\GitLab-Runner\\builds', $BuildsPath
    $configContent = $configContent -replace 'D:\\GitLab-Runner\\cache', $CachePath
    $configContent = $configContent -replace 'your-registration-token-here', $RegistrationToken
    Set-Content $configDest $configContent
    Write-Success "配置文件已更新"
} else {
    Write-Warning "未找到配置文件模板，将使用默认配置"
}

# 注册 Runner
Write-Info "注册 GitLab Runner..."
try {
    $registerArgs = @(
        "register",
        "--non-interactive",
        "--url", $GitLabUrl,
        "--registration-token", $RegistrationToken,
        "--name", $RunnerName,
        "--executor", "shell",
        "--shell", "powershell",
        "--tag-list", "windows,shell,nodejs",
        "--builds-dir", $BuildsPath,
        "--cache-dir", $CachePath
    )
    
    & .\gitlab-runner.exe @registerArgs
    Write-Success "GitLab Runner 注册完成"
} catch {
    Write-ErrorMsg "注册 Runner 失败: $($_.Exception.Message)"
    Write-Info "您可以稍后手动注册 Runner"
}

# 启动服务
Write-Info "启动 GitLab Runner 服务..."
try {
    Start-Service -Name "gitlab-runner"
    Write-Success "GitLab Runner 服务已启动"
} catch {
    Write-ErrorMsg "启动服务失败: $($_.Exception.Message)"
    exit 1
}

# 验证安装
Write-Info "验证安装..."
Start-Sleep -Seconds 3

$service = Get-Service -Name "gitlab-runner" -ErrorAction SilentlyContinue
if ($service -and $service.Status -eq "Running") {
    Write-Success "GitLab Runner 服务运行正常"
} else {
    Write-ErrorMsg "GitLab Runner 服务未正常运行"
}

# 显示版本信息
try {
    $version = & .\gitlab-runner.exe --version
    Write-Success "安装版本: $version"
} catch {
    Write-Warning "无法获取版本信息"
}

# 配置防火墙（可选）
Write-Info "配置 Windows 防火墙..."
try {
    # 允许 GitLab Runner 通过防火墙
    New-NetFirewallRule -DisplayName "GitLab Runner" -Direction Inbound -Program $runnerPath -Action Allow -ErrorAction SilentlyContinue
    Write-Success "防火墙规则已添加"
} catch {
    Write-Warning "添加防火墙规则失败，可能需要手动配置"
}

# 安装完成
Write-Success "🎉 GitLab Runner 安装完成！"
Write-Info ""
Write-Info "安装信息:"
Write-Info "  安装路径: $InstallPath"
Write-Info "  构建目录: $BuildsPath"
Write-Info "  缓存目录: $CachePath"
Write-Info "  配置文件: $configDest"
Write-Info ""
Write-Info "管理命令:"
Write-Info "  查看状态: Get-Service gitlab-runner"
Write-Info "  启动服务: Start-Service gitlab-runner"
Write-Info "  停止服务: Stop-Service gitlab-runner"
Write-Info "  重启服务: Restart-Service gitlab-runner"
Write-Info ""
Write-Info "Runner 管理:"
Write-Info "  验证配置: .\gitlab-runner.exe verify"
Write-Info "  查看日志: .\gitlab-runner.exe --debug run"
Write-Info "  列出 Runner: .\gitlab-runner.exe list"
Write-Info ""
Write-Warning "请确保在 GitLab 项目设置中启用了相应的 Runner"
